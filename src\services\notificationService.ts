import http from '@/lib/http'
import { createSearchParams } from '@/lib/utils'
import type {
  Camera,
  CreateCameraRequest,
  CreateUserRequest,
  PaginatedResponse,
  QueryParams,
  SuccessResApi,
  User,
} from '@/types'

export interface CameraQueryParams {
  premise_id?: string
}

class UserService {
  private readonly baseUrl = '/notifications'

  async getAll(params: QueryParams): Promise<PaginatedResponse<Notification>> {
    const url =
      params && Object.keys(params).length > 0
        ? `${this.baseUrl}?${createSearchParams(params)}`
        : this.baseUrl

    const response = await http.get<SuccessResApi<PaginatedResponse<Notification>>>(url, {
      baseUrl: process.env.NEXT_PUBLIC_NOTIFICATION_ENDPOINT as string,
    })
    return response.payload.data
  }
}

export const userService = new UserService()
